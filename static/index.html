<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>随机匹配聊天室</title>
    <style>
        :root {
            /* 深色主题变量 */
            --bg-primary: hsl(222.2 84% 4.9%);
            --bg-secondary: hsl(217.2 32.6% 17.5%);
            --text-primary: hsl(210 40% 98%);
            --text-secondary: hsl(215.4 16.3% 46.9%);
            --border-color: hsl(217.2 32.6% 17.5%);
            --accent-color: hsl(221.2 83.2% 53.3%);
            --error-color: hsl(0 62.8% 30.6%);
            --success-color: hsl(142.1 76.2% 36.3%);
        }

        [data-theme="light"] {
            /* 浅色主题变量 */
            --bg-primary: hsl(0 0% 100%);
            --bg-secondary: hsl(210 40% 98%);
            --text-primary: hsl(222.2 84% 4.9%);
            --text-secondary: hsl(215.4 16.3% 46.9%);
            --border-color: hsl(214.3 31.8% 91.4%);
            --accent-color: hsl(221.2 83.2% 53.3%);
            --error-color: hsl(0 62.8% 50.6%);
            --success-color: hsl(142.1 76.2% 36.3%);
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            padding: 0;
            transition: background-color 0.3s ease, color 0.3s ease;
            /* 移动端优化 */
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -webkit-tap-highlight-color: transparent;
        }

        .container {
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            padding: 1rem;
            width: 100%;
            max-width: 768px;
            min-height: 600px;
            margin: 0.5rem;
            position: relative;
            transition: background-color 0.3s ease, border-color 0.3s ease;
            /* 移动端适配 */
            height: calc(100vh - 1rem);
            display: flex;
            flex-direction: column;
        }

        /* 移动端响应式设计 */
        @media (max-width: 768px) {
            body {
                align-items: flex-start;
                padding: 0;
            }
            
            .container {
                border-radius: 0;
                margin: 0;
                height: 100vh;
                max-width: 100%;
                border: none;
                box-shadow: none;
            }
        }

        .logo {
            text-align: center;
            margin-bottom: 1.5rem;
            position: relative;
        }

        .theme-toggle {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 1.25rem;
            transition: all 0.3s ease;
            color: var(--text-primary);
            /* 移动端优化 */
            -webkit-appearance: none;
            touch-action: manipulation;
        }

        .theme-toggle:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-1px);
        }

        .theme-toggle:active {
            transform: translateY(1px);
        }

        .logo h1 {
            color: var(--text-primary);
            font-size: 1.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            transition: color 0.3s ease;
        }

        .logo p {
            color: var(--text-secondary);
            font-size: 0.875rem;
            transition: color 0.3s ease;
        }

        /* 移动端 logo 优化 */
        @media (max-width: 768px) {
            .logo {
                margin-bottom: 1rem;
            }
            
            .logo h1 {
                font-size: 1.5rem;
            }
            
            .logo p {
                font-size: 0.75rem;
            }
        }

        /* 匹配页面样式 */
        .match-page {
            text-align: center;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .input-group {
            margin-bottom: 1.5rem;
        }

        .input-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            font-weight: 500;
            font-size: 0.875rem;
            transition: color 0.3s ease;
        }

        .input-with-button {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .input-group input {
            flex: 1;
            padding: 0.875rem 1rem;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.2s ease;
            /* 移动端优化 */
            -webkit-appearance: none;
            -webkit-border-radius: 6px;
        }

        .random-name-btn {
            padding: 0.875rem;
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            font-size: 1.125rem;
            transition: all 0.2s ease;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            /* 移动端优化 */
            -webkit-appearance: none;
            touch-action: manipulation;
        }

        .random-name-btn:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-1px);
        }

        .random-name-btn:active {
            transform: translateY(1px);
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
        }

        .input-group input::placeholder {
            color: var(--text-secondary);
        }

        /* 移动端输入框优化 */
        @media (max-width: 768px) {
            .input-group input {
                padding: 1rem;
                font-size: 1.125rem;
                /* 防止iOS自动缩放 */
                transform: scale(1);
            }
            
            .random-name-btn {
                width: 52px;
                height: 52px;
                font-size: 1.25rem;
            }
        }

        .match-btn {
            width: 100%;
            padding: 0.875rem 1rem;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            /* 移动端优化 */
            -webkit-appearance: none;
            touch-action: manipulation;
        }

        .match-btn:hover:not(:disabled) {
            background: hsl(221.2 83.2% 48.3%);
        }

        .match-btn:active {
            transform: translateY(1px);
        }

        .match-btn:disabled {
            background: var(--bg-secondary);
            color: var(--text-secondary);
            cursor: not-allowed;
        }

        /* 移动端按钮优化 */
        @media (max-width: 768px) {
            .match-btn {
                padding: 1rem;
                font-size: 1.125rem;
                min-height: 48px;
            }
        }

        .status {
            margin-top: 1rem;
            padding: 0.75rem;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .status.matching {
            background: rgba(59, 130, 246, 0.1);
            color: var(--accent-color);
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .status.failed {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid hsl(221.2 83.2% 53.3%);
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 聊天页面样式 - Thread UI 风格 */
        .chat-page {
            display: none;
            height: 500px;
            flex-direction: column;
            flex: 1;
        }

        .chat-page.show {
            display: flex;
        }

        .chat-header {
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: 1rem;
            border: 1px solid var(--border-color);
            border-radius: 6px 6px 0 0;
            margin: -1rem -1rem 0 -1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .chat-info h3 {
            margin-bottom: 0.25rem;
            font-size: 1rem;
            font-weight: 600;
        }

        .chat-info p {
            color: var(--text-secondary);
            font-size: 0.75rem;
        }

        .leave-btn {
            background: var(--error-color);
            color: white;
            border: none;
            padding: 0.5rem 0.75rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.2s ease;
            /* 移动端优化 */
            -webkit-appearance: none;
            touch-action: manipulation;
            min-height: 36px;
        }

        .leave-btn:hover {
            background: hsl(0 62.8% 25.6%);
        }

        .leave-btn:active {
            transform: translateY(1px);
        }

        /* 移动端聊天头部优化 */
        @media (max-width: 768px) {
            .chat-page {
                height: calc(100vh - 2rem);
            }
            
            .chat-info h3 {
                font-size: 0.875rem;
            }
            
            .chat-info p {
                font-size: 0.625rem;
            }
            
            .leave-btn {
                padding: 0.5rem;
                font-size: 0.625rem;
                min-height: 32px;
            }
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            margin: 0;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            /* 移动端滚动优化 */
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        /* Thread UI 消息组 */
        .message-group {
            display: flex;
            gap: 0.75rem;
            align-items: flex-start;
        }

        .message-group.own {
            flex-direction: row-reverse;
        }

        .avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: hsl(221.2 83.2% 53.3%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            font-weight: 600;
            flex-shrink: 0;
        }

        .message-content {
            max-width: 70%;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            align-items: flex-start;
        }

        .message-group.own .message-content {
            align-items: flex-end;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .message-group.own .message-header {
            flex-direction: row-reverse;
        }

        .sender-name {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .message-time {
            font-size: 0.625rem;
            color: var(--text-secondary);
        }

        /* 移动端消息优化 */
        @media (max-width: 768px) {
            .messages {
                padding: 0.75rem 0.25rem;
                gap: 0.5rem;
            }
            
            .message-group {
                gap: 0.25rem;
            }
            
            .avatar {
                width: 28px;
                height: 28px;
                font-size: 0.625rem;
            }
            
            .message-content {
                max-width: 75%;
            }
            
            .sender-name {
                font-size: 0.625rem;
            }
            
            .message-time {
                font-size: 0.5rem;
            }
        }

        .message {
            padding: 0.75rem;
            border-radius: 12px;
            word-wrap: break-word;
            font-size: 0.875rem;
            line-height: 1.4;
            position: relative;
            display: inline-block;
            max-width: 100%;
            /* 移动端文本选择 */
            -webkit-user-select: text;
            user-select: text;
        }

        .message.own {
            background: var(--accent-color);
            color: white;
        }

        .message.other {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .message.image {
            padding: 0.5rem;
            background: transparent;
        }

        .message img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .message img:hover {
            transform: scale(1.02);
        }

        /* 移动端消息气泡优化 */
        @media (max-width: 768px) {
            .message {
                padding: 0.625rem;
                font-size: 0.875rem;
                border-radius: 10px;
            }
            
            .message img {
                max-width: 150px;
                max-height: 150px;
            }
        }

        /* 系统消息分割线 */
        .system-message {
            display: flex;
            align-items: center;
            margin: 1rem 0;
        }

        .system-message::before,
        .system-message::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--border-color);
        }

        .system-text {
            padding: 0 1rem;
            font-size: 0.75rem;
            color: var(--text-secondary);
            white-space: nowrap;
        }

        .image-preview {
            display: none;
            padding: 1rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .image-preview img {
            max-width: 150px;
            max-height: 150px;
            border-radius: 6px;
            display: block;
            margin: 0 auto;
        }

        .preview-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            margin-top: 0.75rem;
        }

        .preview-btn {
            padding: 0.5rem 0.75rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .preview-send {
            background: hsl(142.1 76.2% 36.3%);
            color: white;
        }

        .preview-send:hover {
            background: hsl(142.1 76.2% 31.3%);
        }

        .preview-cancel {
            background: hsl(0 62.8% 30.6%);
            color: white;
        }

        .preview-cancel:hover {
            background: hsl(0 62.8% 25.6%);
        }

        .message-input-group {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            padding: 1rem;
            border-top: 1px solid var(--border-color);
            margin: 0 -1rem -1rem -1rem;
            flex-shrink: 0;
            /* 移动端优化 */
            background: var(--bg-primary);
            transition: all 0.3s ease;
            /* 防止虚拟键盘推起布局 */
            position: relative;
        }

        .image-btn {
            padding: 0.5rem;
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.2s ease;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            /* 移动端优化 */
            -webkit-appearance: none;
            touch-action: manipulation;
        }

        .image-btn:hover {
            background: var(--accent-color);
            color: white;
        }

        .image-btn:active {
            transform: translateY(1px);
        }

        .message-input {
            flex: 1;
            padding: 0.5rem 0.75rem;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 0.875rem;
            min-height: 36px;
            max-height: 120px;
            line-height: 1.4;
            transition: all 0.2s ease;
            /* 移动端优化 */
            -webkit-appearance: none;
            resize: none;
            overflow-y: auto;
            /* 多行输入优化 */
            -webkit-overflow-scrolling: touch;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--accent-color);
        }

        .message-input::placeholder {
            color: var(--text-secondary);
        }

        .send-btn {
            padding: 0.5rem 1rem;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 0.875rem;
            height: 36px;
            transition: all 0.2s ease;
            flex-shrink: 0;
            /* 移动端优化 */
            -webkit-appearance: none;
            touch-action: manipulation;
        }

        .send-btn:hover:not(:disabled) {
            background: hsl(221.2 83.2% 48.3%);
        }

        .send-btn:active {
            transform: translateY(1px);
        }

        .send-btn:disabled {
            background: var(--bg-secondary);
            color: var(--text-secondary);
            cursor: not-allowed;
        }

        /* 移动端输入区优化 */
        @media (max-width: 768px) {
            .message-input-group {
                padding: 0.75rem;
                gap: 0.375rem;
                /* 保持在屏幕底部 */
                position: sticky;
                bottom: 0;
                align-items: flex-end;
            }
            
            .image-btn {
                width: 40px;
                height: 40px;
                font-size: 0.875rem;
                align-self: flex-end;
            }
            
            .message-input {
                font-size: 1rem;
                min-height: 40px;
                max-height: 100px;
                padding: 0.625rem 0.75rem;
                /* 防止iOS自动缩放 */
                transform: scale(1);
            }
            
            .send-btn {
                height: 40px;
                padding: 0.625rem 0.875rem;
                font-size: 0.875rem;
                min-width: 60px;
                align-self: flex-end;
            }
        }

        .hidden {
            display: none !important;
        }

        .hidden-file-input {
            display: none;
        }
    </style>
</head>
<body>
<div class="container">
    <!-- Logo -->
    <div class="logo">
        <button id="themeToggle" class="theme-toggle" title="切换主题">🌙</button>
        <h1>💬 聊天室</h1>
        <p>随机匹配，畅聊无限</p>
    </div>

    <!-- 匹配页面 -->
    <div id="matchPage" class="match-page">
        <div class="input-group">
            <label for="username">用户名</label>
            <div class="input-with-button">
                <input type="text" id="username" placeholder="请输入您的用户名" maxlength="20">
                <button id="randomNameBtn" class="random-name-btn" title="生成随机用户名">🎲</button>
            </div>
        </div>

        <button id="matchBtn" class="match-btn">开始匹配</button>

        <div id="status" class="status hidden"></div>
    </div>

    <!-- 聊天页面 -->
    <div id="chatPage" class="chat-page">
        <div class="chat-header">
            <div class="chat-info">
                <h3 id="chatTitle">聊天中...</h3>
                <p id="partnerInfo">正在连接...</p>
            </div>
            <button id="leaveBtn" class="leave-btn">离开</button>
        </div>

        <div id="messages" class="messages"></div>

        <div id="imagePreview" class="image-preview">
            <img id="previewImg" src="" alt="图片预览">
            <div class="preview-actions">
                <button id="sendImageBtn" class="preview-btn preview-send">发送图片</button>
                <button id="cancelImageBtn" class="preview-btn preview-cancel">取消</button>
            </div>
        </div>

        <div class="message-input-group">
            <input type="file" id="imageInput" class="hidden-file-input" accept="image/*">
            <button id="imageBtn" class="image-btn" title="发送图片">📷</button>
            <textarea id="messageInput" class="message-input" placeholder="输入消息..." maxlength="500" rows="1"></textarea>
            <button id="sendBtn" class="send-btn">发送</button>
        </div>
    </div>
</div>

<script>
    const backendUrl = window.location.hostname === 'localhost' ? 'http://localhost:9093' : 'https://your-backend-domain.com';
    const websocketUrl = window.location.hostname === 'localhost' ? 'ws://localhost:9093' : 'wss://your-backend-domain.com';

    class ChatRoom {
        constructor() {
            this.currentUserId = '';
            this.currentRoomId = '';
            this.currentPartnerId = '';
            this.websocket = null;
            this.isMatching = false;
            this.matchInterval = null;
            this.selectedImageFile = null;
            this.isDarkTheme = true; // 默认深色主题

            this.initElements();
            this.bindEvents();
            this.initTheme();
        }

        initElements() {
            // 匹配页面元素
            this.matchPage = document.getElementById('matchPage');
            this.usernameInput = document.getElementById('username');
            this.randomNameBtn = document.getElementById('randomNameBtn');
            this.matchBtn = document.getElementById('matchBtn');
            this.statusDiv = document.getElementById('status');

            // 主题切换元素
            this.themeToggle = document.getElementById('themeToggle');

            // 聊天页面元素
            this.chatPage = document.getElementById('chatPage');
            this.chatTitle = document.getElementById('chatTitle');
            this.partnerInfo = document.getElementById('partnerInfo');
            this.messagesDiv = document.getElementById('messages');
            this.messageInput = document.getElementById('messageInput');
            this.sendBtn = document.getElementById('sendBtn');
            this.leaveBtn = document.getElementById('leaveBtn');

            // 图片相关元素
            this.imageBtn = document.getElementById('imageBtn');
            this.imageInput = document.getElementById('imageInput');
            this.imagePreview = document.getElementById('imagePreview');
            this.previewImg = document.getElementById('previewImg');
            this.sendImageBtn = document.getElementById('sendImageBtn');
            this.cancelImageBtn = document.getElementById('cancelImageBtn');
        }

        bindEvents() {
            this.matchBtn.addEventListener('click', () => this.startMatching());
            this.randomNameBtn.addEventListener('click', () => this.generateRandomName());
            this.sendBtn.addEventListener('click', () => this.sendMessage());
            this.leaveBtn.addEventListener('click', () => this.leaveChat());

            // 主题切换事件
            this.themeToggle.addEventListener('click', () => this.toggleTheme());

            // 图片相关事件
            this.imageBtn.addEventListener('click', () => this.imageInput.click());
            this.imageInput.addEventListener('change', (e) => this.handleImageSelect(e));
            this.sendImageBtn.addEventListener('click', () => this.sendImage());
            this.cancelImageBtn.addEventListener('click', () => this.cancelImage());

            // 输入框自动调整高度
            this.messageInput.addEventListener('input', () => this.autoResizeInput());
            this.messageInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                } else if (e.key === 'Enter' && e.shiftKey) {
                    // Shift+Enter 换行，延迟调整高度
                    setTimeout(() => this.autoResizeInput(), 0);
                }
            });

            // 用户名输入回车开始匹配
            this.usernameInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.startMatching();
                }
            });

            // 页面关闭或刷新确认
            this.setupBeforeUnloadWarning();

            // 移动端优化：处理虚拟键盘
            this.handleMobileKeyboard();
        }

        handleMobileKeyboard() {
            if (!this.isMobile()) return;

            // 输入框获得焦点时的处理
            this.messageInput.addEventListener('focus', () => {
                setTimeout(() => {
                    this.scrollToBottom();
                }, 300);
            });

            // 输入框失去焦点时的处理
            this.messageInput.addEventListener('blur', () => {
                setTimeout(() => {
                    this.scrollToBottom();
                }, 100);
            });

            // 窗口大小变化时的处理
            window.addEventListener('resize', () => {
                setTimeout(() => {
                    this.scrollToBottom();
                }, 100);
            });
        }

        isMobile() {
            return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        scrollToBottom() {
            if (this.messagesDiv) {
                this.messagesDiv.scrollTop = this.messagesDiv.scrollHeight;
            }
        }

        autoResizeInput() {
            const textarea = this.messageInput;
            // 重置高度以获取真实内容高度
            textarea.style.height = 'auto';
            
            // 计算所需高度
            const scrollHeight = textarea.scrollHeight;
            const maxHeight = this.isMobile() ? 100 : 120; // 移动端限制更低
            const minHeight = this.isMobile() ? 40 : 36;
            
            // 设置新高度，但不超过最大高度
            const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
            textarea.style.height = newHeight + 'px';
            
            // 如果内容超过最大高度，显示滚动条
            if (scrollHeight > maxHeight) {
                textarea.style.overflowY = 'auto';
            } else {
                textarea.style.overflowY = 'hidden';
            }
            
            // 调整后滚动到底部
            setTimeout(() => this.scrollToBottom(), 0);
        }

        initTheme() {
            // 从 localStorage 获取保存的主题设置
            const savedTheme = localStorage.getItem('chatTheme');
            if (savedTheme === 'light') {
                this.isDarkTheme = false;
                document.documentElement.setAttribute('data-theme', 'light');
                this.updateThemeIcon();
            }
        }

        toggleTheme() {
            this.isDarkTheme = !this.isDarkTheme;
            
            if (this.isDarkTheme) {
                document.documentElement.removeAttribute('data-theme');
                localStorage.setItem('chatTheme', 'dark');
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
                localStorage.setItem('chatTheme', 'light');
            }
            
            this.updateThemeIcon();
        }

        updateThemeIcon() {
            // 更新主题切换按钮的图标
            this.themeToggle.textContent = this.isDarkTheme ? '🌞' : '🌙';
            this.themeToggle.title = this.isDarkTheme ? '切换到浅色主题' : '切换到深色主题';
        }

        showStatus(message, type = 'matching') {
            this.statusDiv.className = `status ${type}`;
            this.statusDiv.innerHTML = type === 'matching'
                ? `<div class="loading"></div>${message}`
                : message;
            this.statusDiv.classList.remove('hidden');
        }

        hideStatus() {
            this.statusDiv.classList.add('hidden');
        }

        async startMatching() {
            const username = this.usernameInput.value.trim();
            if (!username) {
                alert('请输入用户名');
                return;
            }

            if (this.isMatching) return;

            this.currentUserId = username;
            this.isMatching = true;
            this.matchBtn.disabled = true;
            this.matchBtn.textContent = '匹配中...';

            await this.attemptMatch();
        }

        async attemptMatch() {
            this.showStatus('正在寻找聊天伙伴...');

            try {
                const response = await fetch(`${backendUrl}/api/match`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: this.currentUserId
                    })
                });

                const data = await response.json();

                if (data.matched) {
                    // 匹配成功
                    this.currentRoomId = data.room_id;
                    this.currentPartnerId = data.partner_id;
                    this.hideStatus();
                    this.enterChatRoom();
                } else {
                    // 匹配失败，5秒后重试
                    // this.showStatus('暂时没有找到聊天伙伴，5秒后重新匹配...', 'failed');
                    this.matchInterval = setTimeout(() => {
                        if (this.isMatching) {
                            this.attemptMatch();
                        }
                    }, 5000);
                }
            } catch (error) {
                console.error('匹配请求失败:', error);
                this.showStatus('网络错误，5秒后重新匹配...', 'failed');
                this.matchInterval = setTimeout(() => {
                    if (this.isMatching) {
                        this.attemptMatch();
                    }
                }, 5000);
            }
        }

        enterChatRoom() {
            // 隐藏匹配页面，显示聊天页面
            this.matchPage.classList.add('hidden');
            this.chatPage.classList.add('show');

            // 更新聊天界面信息
            this.chatTitle.textContent = `房间: ${this.currentRoomId}`;
            this.partnerInfo.textContent = `对方: ${this.currentPartnerId}`;

            // 建立WebSocket连接
            this.connectWebSocket();

            // 初始化输入框高度
            this.autoResizeInput();

            // 聚焦到消息输入框
            this.messageInput.focus();
        }

        connectWebSocket() {
            const wsUrl = `${websocketUrl}/api/ws?room=${this.currentRoomId}&user=${this.currentUserId}`;

            try {
                this.websocket = new WebSocket(wsUrl);

                this.websocket.onopen = () => {
                    console.log('WebSocket连接已建立');
                    this.addSystemMessage('已连接到聊天室，开始聊天吧！');
                };

                this.websocket.onmessage = (event) => {
                    try {
                        const message = JSON.parse(event.data);
                        if (message.type === 'image') {
                            this.addImageMessage(message.content, message.from, message.from !== this.currentUserId);
                        } else {
                            this.addMessage(message.content, message.from, message.from !== this.currentUserId);
                        }
                    } catch (error) {
                        console.error('解析消息失败:', error);
                    }
                };

                this.websocket.onclose = () => {
                    console.log('WebSocket连接已关闭');
                    this.addSystemMessage('连接已断开');
                };

                this.websocket.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    this.addSystemMessage('连接错误，请刷新页面重试');
                };
            } catch (error) {
                console.error('WebSocket连接失败:', error);
                this.addSystemMessage('无法连接到聊天服务器');
            }
        }

        sendMessage() {
            const content = this.messageInput.value.trim();
            if (!content || !this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                return;
            }

            const message = {
                from: this.currentUserId,
                content: content,
                type: 'text'
            };

            try {
                this.websocket.send(JSON.stringify(message));
                this.addMessage(content, this.currentUserId, false);
                this.messageInput.value = '';
                // 发送消息后重置输入框高度
                this.autoResizeInput();
            } catch (error) {
                console.error('发送消息失败:', error);
                this.addSystemMessage('消息发送失败');
            }
        }

        handleImageSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                alert('请选择图片文件');
                return;
            }

            // 验证文件大小（限制为5MB）
            if (file.size > 50 * 1024 * 1024) {
                alert('图片大小不能超过5MB');
                return;
            }

            this.selectedImageFile = file;

            // 显示预览
            const reader = new FileReader();
            reader.onload = (e) => {
                this.previewImg.src = e.target.result;
                this.imagePreview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }

        sendImage() {
            if (!this.selectedImageFile || !this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const message = {
                    from: this.currentUserId,
                    content: e.target.result, // base64 图片数据
                    type: 'image'
                };

                try {
                    this.websocket.send(JSON.stringify(message));
                    this.addImageMessage(e.target.result, this.currentUserId, false);
                    this.cancelImage();
                } catch (error) {
                    console.error('发送图片失败:', error);
                    this.addSystemMessage('图片发送失败');
                }
            };
            reader.readAsDataURL(this.selectedImageFile);
        }

        cancelImage() {
            this.selectedImageFile = null;
            this.previewImg.src = '';
            this.imagePreview.style.display = 'none';
            this.imageInput.value = '';
        }

        addMessage(content, from, isOther) {
            const messageGroup = document.createElement('div');
            messageGroup.className = `message-group ${isOther ? '' : 'own'}`;

            // 头像
            const avatar = document.createElement('div');
            avatar.className = 'avatar';
            avatar.textContent = from.charAt(0).toUpperCase();

            // 消息内容容器
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // 消息头部（用户名和时间）
            const messageHeader = document.createElement('div');
            messageHeader.className = 'message-header';

            const senderName = document.createElement('span');
            senderName.className = 'sender-name';
            senderName.textContent = from;

            const messageTime = document.createElement('span');
            messageTime.className = 'message-time';
            messageTime.textContent = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

            messageHeader.appendChild(senderName);
            messageHeader.appendChild(messageTime);

            // 消息气泡
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isOther ? 'other' : 'own'}`;
            messageDiv.textContent = content;

            messageContent.appendChild(messageHeader);
            messageContent.appendChild(messageDiv);

            messageGroup.appendChild(avatar);
            messageGroup.appendChild(messageContent);

            this.messagesDiv.appendChild(messageGroup);
            this.scrollToBottom();
        }

        addImageMessage(imageSrc, from, isOther) {
            const messageGroup = document.createElement('div');
            messageGroup.className = `message-group ${isOther ? '' : 'own'}`;

            // 头像
            const avatar = document.createElement('div');
            avatar.className = 'avatar';
            avatar.textContent = from.charAt(0).toUpperCase();

            // 消息内容容器
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // 消息头部
            const messageHeader = document.createElement('div');
            messageHeader.className = 'message-header';

            const senderName = document.createElement('span');
            senderName.className = 'sender-name';
            senderName.textContent = from;

            const messageTime = document.createElement('span');
            messageTime.className = 'message-time';
            messageTime.textContent = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

            messageHeader.appendChild(senderName);
            messageHeader.appendChild(messageTime);

            // 图片消息
            const messageDiv = document.createElement('div');
            messageDiv.className = `message image ${isOther ? 'other' : 'own'}`;

            const img = document.createElement('img');
            img.src = imageSrc;
            img.alt = '图片消息';
            img.onclick = () => this.showImageModal(imageSrc);

            messageDiv.appendChild(img);
            messageContent.appendChild(messageHeader);
            messageContent.appendChild(messageDiv);

            messageGroup.appendChild(avatar);
            messageGroup.appendChild(messageContent);

            this.messagesDiv.appendChild(messageGroup);
            this.scrollToBottom();
        }

        showImageModal(imageSrc) {
            // 创建模态框显示大图
            const modal = document.createElement('div');
            modal.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                    cursor: pointer;
                `;

            const img = document.createElement('img');
            img.src = imageSrc;
            img.style.cssText = `
                    max-width: 90%;
                    max-height: 90%;
                    border-radius: 8px;
                `;

            modal.appendChild(img);
            document.body.appendChild(modal);

            // 点击关闭模态框
            modal.onclick = () => {
                document.body.removeChild(modal);
            };
        }

        addSystemMessage(content) {
            const systemMessage = document.createElement('div');
            systemMessage.className = 'system-message';
            
            const systemText = document.createElement('span');
            systemText.className = 'system-text';
            systemText.textContent = content;
            
            systemMessage.appendChild(systemText);
            this.messagesDiv.appendChild(systemMessage);
            this.scrollToBottom();
        }

        leaveChat() {
            if (confirm('确定要离开聊天室吗？')) {
                this.cleanup();
                this.resetToMatchPage();
            }
        }

        cleanup() {
            // 清理WebSocket连接
            if (this.websocket) {
                this.websocket.close();
                this.websocket = null;
            }

            // 清理匹配定时器
            if (this.matchInterval) {
                clearTimeout(this.matchInterval);
                this.matchInterval = null;
            }

            // 重置状态
            this.isMatching = false;
            this.currentRoomId = '';
            this.currentPartnerId = '';
        }

        generateRandomName() {
            const adjectives = [
                '快乐的', '聪明的', '勇敢的', '温柔的', '活泼的', '可爱的', '优雅的', '幽默的',
                '热情的', '友善的', '机智的', '淡定的', '开朗的', '文艺的', '神秘的', '阳光的',
                '睿智的', '潇洒的', '清新的', '灵动的', '温暖的', '浪漫的', '自信的', '独特的',
                '安静的', '沉稳的', '活力的', '优秀的', '善良的', '纯真的', '梦幻的', '酷炫的',
                '慵懒的', '俏皮的', '典雅的', '淘气的', '呆萌的', '迷人的', '乖巧的', '灿烂的',
                '温和的', '坚强的', '柔软的', '清雅的', '古灵的', '精怪的', '甜美的', '帅气的',
                '迷糊的', '机敏的', '顽皮的', '萌萌的', '飘逸的', '静谧的', '欢快的', '闪亮的',
                // 英文形容词
                'Happy', 'Smart', 'Brave', 'Gentle', 'Cute', 'Sunny', 'Active', 'Elegant',
                'Mystery', 'Calm', 'Funny', 'Kind', 'Clever', 'Warm', 'Cool', 'Strong',
                'Dream', 'Sweet', 'Fresh', 'Magic', 'Wild', 'Swift', 'Quiet', 'Bright',
                'Epic', 'Royal', 'Noble', 'Lucky', 'Pure', 'Wise', 'Bold', 'Silent'
            ];
            
            const nouns = [
                '小猫', '小狗', '熊猫', '兔子', '松鼠', '海豚', '企鹅', '狐狸',
                '小鸟', '蝴蝶', '独角兽', '星星', '月亮', '太阳', '彩虹', '云朵',
                '花朵', '树叶', '雪花', '露珠', '微风', '晨光', '夕阳', '流星',
                '小鹿', '天鹅', '蜜蜂', '萤火虫', '海星', '贝壳', '珊瑚', '水母',
                '樱花', '玫瑰', '向日葵', '薰衣草', '茉莉', '百合', '牡丹', '荷花',
                '竹子', '柳树', '梧桐', '银杏', '枫叶', '青苔', '小草', '芦苇',
                '钻石', '珍珠', '翡翠', '水晶', '琥珀', '玛瑙', '紫晶', '宝石',
                '春风', '夏雨', '秋叶', '冬雪', '朝霞', '晚霞', '星河', '银河',
                // 英文名词
                'Cat', 'Dog', 'Rabbit', 'Panda', 'Tiger', 'Lion', 'Elephant', 'Giraffe',
                'Penguin', 'Dolphin', 'Butterfly', 'Bee', 'Bird', 'Swan', 'Peacock', 'Phoenix',
                'Unicorn', 'Dragon', 'Fox', 'Squirrel', 'Deer', 'Hippo', 'Koala', 'Kangaroo',
                'Octopus', 'Starfish', 'Shell', 'Coral', 'Rainbow', 'Star', 'Moon', 'Sun'
            ];
            
            const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
            const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
            
            const randomName = `${randomAdjective}${randomNoun}`;
            this.usernameInput.value = randomName;
            
            // 添加轻微的动画效果
            this.randomNameBtn.style.transform = 'rotate(180deg) translateY(1px)';
            setTimeout(() => {
                this.randomNameBtn.style.transform = '';
            }, 200);
        }

        setupBeforeUnloadWarning() {
            window.addEventListener('beforeunload', (e) => {
                // 只有在聊天状态或匹配状态时才显示确认框
                if (this.isInChatOrMatching()) {
                    const message = '您正在聊天中，离开页面将断开连接。确定要离开吗？';
                    e.preventDefault();
                    e.returnValue = message;
                    return message;
                }
            });
        }

        isInChatOrMatching() {
            // 如果正在匹配或已经在聊天页面，则需要确认
            return this.isMatching || this.chatPage.classList.contains('show');
        }

        resetToMatchPage() {
            // 显示匹配页面，隐藏聊天页面
            this.chatPage.classList.remove('show');
            this.matchPage.classList.remove('hidden');

            // 重置按钮状态
            this.matchBtn.disabled = false;
            this.matchBtn.textContent = '开始匹配';

            // 清空消息和图片预览
            this.messagesDiv.innerHTML = '';
            this.messageInput.value = '';
            this.cancelImage();

            // 隐藏状态信息
            this.hideStatus();
        }
    }

    // 初始化聊天室
    document.addEventListener('DOMContentLoaded', () => {
        new ChatRoom();
    });
</script>
</body>
</html>